import { createAISDKTools } from "@agentic/ai-sdk";
import { WikipediaClient } from "@agentic/wikipedia";
import { openai } from "@ai-sdk/openai";
import { withTracing } from "@posthog/ai";
import {
  appendClientMessage,
  appendResponseMessages,
  createIdGenerator,
  streamText,
  tool,
  type UIMessage,
} from "ai";
import { headers } from "next/headers";
import { PostHog } from "posthog-node";
import { z } from "zod";
import { env } from "@/env";
import { loadChat, upsertMessage } from "@/lib/actions/ai-chat";
import { createResource } from "@/lib/actions/resources";
import { systemPrompt } from "@/lib/ai/prompts";
// Admin tools
import { getUserStats } from "@/lib/ai/tools/admin/getUserStats";
import {
  banUser,
  changeUserRole,
  searchUsers,
} from "@/lib/ai/tools/admin/manageUser";
import { getPlatformInsights } from "@/lib/ai/tools/admin/platformInsights";
// Existing tools
import { createProject } from "@/lib/ai/tools/createProject";
import { createTemplate } from "@/lib/ai/tools/createTemplates";
import { createTrade, findTradeId } from "@/lib/ai/tools/createTrade";
import { getCompanies } from "@/lib/ai/tools/getCompanies";
import {
  analyzeBidCompetition,
  findRelevantJobs,
  getContractorPerformance,
} from "@/lib/ai/tools/user/contractorTools";
// User tools
import {
  analyzeProjectBids,
  getProjectRecommendations,
  getProjectTimeline,
} from "@/lib/ai/tools/user/projectAssistant";
import { auth } from "@/lib/auth";

export const maxDuration = 30;

export async function POST(req: Request) {
  const { message, chatId }: { message: UIMessage; chatId: string } =
    await req.json();

  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  const phClient = new PostHog(env.NEXT_PUBLIC_POSTHOG_KEY, {
    host: env.NEXT_PUBLIC_POSTHOG_HOST,
  });

  const model = withTracing(openai("gpt-4.1-mini"), phClient, {
    posthogDistinctId: user?.id,
    posthogProperties: { conversation_id: chatId },
  });

  await upsertMessage({ chatId, message, id: message.id });

  const previousMessages = await loadChat(chatId);

  const messages = appendClientMessage({
    messages: previousMessages.map((m) => ({ ...m, content: "" }) as UIMessage),
    message,
  });

  const wikipedia = new WikipediaClient();

  // Create role-based tools
  const baseTools = {
    addResource: tool({
      description:
        "add a resource to your knowledge base. If the user provides a random piece of knowledge unprompted, use this tool to add it to your knowledge base.",
      parameters: z.object({
        content: z.string().describe("The content of the resource"),
      }),
      execute: async ({ content }) => createResource({ content }),
    }),
    createProject: createProject(),
    getCompanies: getCompanies(),
    createTemplate: createTemplate(),
    createTrade: createTrade(),
    findTradeId: findTradeId(),
    ...createAISDKTools(wikipedia),
  };

  // Add role-specific tools
  const roleSpecificTools: Record<string, unknown> = {};

  if (user?.role === "admin") {
    roleSpecificTools.getUserStats = getUserStats();
    roleSpecificTools.searchUsers = searchUsers();
    roleSpecificTools.changeUserRole = changeUserRole();
    roleSpecificTools.banUser = banUser();
    roleSpecificTools.getPlatformInsights = getPlatformInsights();
  }

  if (user?.role === "homeowner") {
    roleSpecificTools.getProjectRecommendations = getProjectRecommendations();
    roleSpecificTools.analyzeProjectBids = analyzeProjectBids();
    roleSpecificTools.getProjectTimeline = getProjectTimeline();
  }

  if (user?.role === "contractor") {
    roleSpecificTools.findRelevantJobs = findRelevantJobs();
    roleSpecificTools.analyzeBidCompetition = analyzeBidCompetition();
    roleSpecificTools.getContractorPerformance = getContractorPerformance();
  }

  const allTools = { ...baseTools, ...roleSpecificTools };

  const result = streamText({
    model,
    system: systemPrompt({
      userName: user?.name as string,
      userRole: user?.role as string,
      userId: user?.id as string,
    }),
    messages,
    tools: allTools,
    experimental_generateMessageId: createIdGenerator({
      prefix: "msgs",
      size: 16,
    }),
    maxSteps: 5,
    async onFinish({ response }) {
      // biome-ignore lint/style/noNonNullAssertion: Required
      const newMessage = appendResponseMessages({
        messages,
        responseMessages: response.messages,
      }).at(-1)!;

      await upsertMessage({
        chatId,
        message: newMessage as UIMessage,
        id: newMessage.id,
      });
    },
  });

  phClient.shutdown();

  return result.toDataStreamResponse({
    getErrorMessage: errorHandler,
  });
}

function errorHandler(error: unknown) {
  console.error("Error streaming text:", error);
  return JSON.stringify(error);
}
