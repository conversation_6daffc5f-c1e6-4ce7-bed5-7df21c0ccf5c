import { tool } from "ai";
import { avg, count, desc, eq, gte, sum } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, organization, property, user } from "@/db/schema";

export const getPlatformInsights = () =>
  tool({
    description:
      "Get business intelligence and platform insights for admin decision making",
    parameters: z.object({
      insightType: z
        .enum([
          "overview",
          "user-engagement",
          "job-performance",
          "contractor-analysis",
          "revenue-trends",
          "geographic-distribution",
        ])
        .describe("Type of insights to generate"),
      timeframe: z
        .enum(["7d", "30d", "90d", "1y"])
        .optional()
        .default("30d")
        .describe("Time frame for analysis"),
    }),
    execute: async ({ insightType, timeframe }) => {
      const now = new Date();
      let startDate: Date;

      switch (timeframe) {
        case "7d":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "30d":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case "90d":
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case "1y":
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
      }

      try {
        switch (insightType) {
          case "overview":
            return await getOverviewInsights(startDate);
          case "user-engagement":
            return await getUserEngagementInsights(startDate);
          case "job-performance":
            return await getJobPerformanceInsights(startDate);
          case "contractor-analysis":
            return await getContractorAnalysisInsights(startDate);
          case "revenue-trends":
            return await getRevenueTrendsInsights(startDate);
          case "geographic-distribution":
            return await getGeographicInsights();
          default:
            return "Invalid insight type requested.";
        }
      } catch (error) {
        console.error("Error generating platform insights:", error);
        return "Failed to generate platform insights. Please try again.";
      }
    },
  });

async function getOverviewInsights(startDate: Date) {
  const [
    totalUsers,
    newUsers,
    totalJobs,
    newJobs,
    totalBids,
    newBids,
    completedJobs,
    activeOrganizations,
  ] = await Promise.all([
    db.$count(user),
    db.$count(user, gte(user.createdAt, startDate)),
    db.$count(job),
    db.$count(job, gte(job.createdAt, startDate)),
    db.$count(bid),
    db.$count(bid, gte(bid.createdAt, startDate)),
    db.$count(job, eq(job.status, "COMPLETED")),
    db.$count(organization),
  ]);

  return JSON.stringify(
    {
      summary: "Platform Overview Insights",
      metrics: {
        users: { total: totalUsers, new: newUsers },
        jobs: { total: totalJobs, new: newJobs, completed: completedJobs },
        bids: { total: totalBids, new: newBids },
        organizations: { active: activeOrganizations },
      },
      insights: [
        `${newUsers} new users joined in the selected timeframe`,
        `${newJobs} new jobs were posted`,
        `${newBids} new bids were submitted`,
        `Job completion rate: ${totalJobs > 0 ? ((completedJobs / totalJobs) * 100).toFixed(1) : 0}%`,
      ],
    },
    null,
    2,
  );
}

async function getUserEngagementInsights(startDate: Date) {
  const userActivity = await db
    .select({
      role: user.role,
      count: count(user.id),
      verified: count(user.emailVerified),
    })
    .from(user)
    .where(gte(user.createdAt, startDate))
    .groupBy(user.role);

  const recentJobCreators = await db
    .select({
      userId: job.userId,
      jobCount: count(job.id),
    })
    .from(job)
    .where(gte(job.createdAt, startDate))
    .groupBy(job.userId)
    .orderBy(desc(count(job.id)))
    .limit(10);

  return JSON.stringify(
    {
      summary: "User Engagement Analysis",
      newUsersByRole: userActivity,
      topJobCreators: recentJobCreators,
      insights: [
        "Most active user segments and engagement patterns",
        "User onboarding completion rates",
        "Feature adoption metrics",
      ],
    },
    null,
    2,
  );
}

async function getJobPerformanceInsights(startDate: Date) {
  const jobMetrics = await db
    .select({
      status: job.status,
      count: count(job.id),
      avgBudget: avg(job.budget),
    })
    .from(job)
    .where(gte(job.createdAt, startDate))
    .groupBy(job.status);

  const bidMetrics = await db
    .select({
      jobId: bid.jobId,
      bidCount: count(bid.id),
      avgAmount: avg(bid.amount),
    })
    .from(bid)
    .where(gte(bid.createdAt, startDate))
    .groupBy(bid.jobId)
    .limit(20);

  return JSON.stringify(
    {
      summary: "Job Performance Analysis",
      jobsByStatus: jobMetrics,
      bidActivity: bidMetrics,
      insights: [
        "Job posting trends and success rates",
        "Average time to completion",
        "Bid competition levels",
      ],
    },
    null,
    2,
  );
}

async function getContractorAnalysisInsights(startDate: Date) {
  const contractorMetrics = await db
    .select({
      organizationId: bid.organizationId,
      bidCount: count(bid.id),
      winRate: count(bid.id), // This would need more complex calculation
    })
    .from(bid)
    .where(gte(bid.createdAt, startDate))
    .groupBy(bid.organizationId)
    .orderBy(desc(count(bid.id)))
    .limit(20);

  return JSON.stringify(
    {
      summary: "Contractor Performance Analysis",
      topContractors: contractorMetrics,
      insights: [
        "Most active contractors",
        "Bid success rates",
        "Service category performance",
      ],
    },
    null,
    2,
  );
}

async function getRevenueTrendsInsights(startDate: Date) {
  // This would need actual revenue/transaction data
  const jobValues = await db
    .select({
      month: job.createdAt,
      totalValue: sum(job.budget),
      jobCount: count(job.id),
    })
    .from(job)
    .where(gte(job.createdAt, startDate))
    .groupBy(job.createdAt);

  return JSON.stringify(
    {
      summary: "Revenue and Growth Trends",
      monthlyMetrics: jobValues,
      insights: [
        "Platform transaction volume trends",
        "Average project values",
        "Growth trajectory analysis",
      ],
    },
    null,
    2,
  );
}

async function getGeographicInsights() {
  // This would use the PostGIS geometry data from properties
  const propertyDistribution = await db
    .select({
      count: count(property.id),
    })
    .from(property);

  return JSON.stringify(
    {
      summary: "Geographic Distribution Analysis",
      propertyCount: propertyDistribution,
      insights: [
        "Service area coverage",
        "Market penetration by region",
        "Expansion opportunities",
      ],
    },
    null,
    2,
  );
}
