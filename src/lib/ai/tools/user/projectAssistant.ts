import { tool } from "ai";
import { and, desc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, property } from "@/db/schema";

export const getProjectRecommendations = () =>
  tool({
    description:
      "Get personalized project recommendations and insights for homeowners",
    parameters: z.object({
      userId: z.string().describe("The user ID to get recommendations for"),
      projectType: z
        .string()
        .optional()
        .describe("Specific type of project to focus on"),
      budgetRange: z
        .object({
          min: z.number().optional(),
          max: z.number().optional(),
        })
        .optional()
        .describe("Budget range for recommendations"),
    }),
    execute: async ({ userId, projectType, budgetRange }) => {
      try {
        // Get user's properties
        const userProperties = await db.query.property.findMany({
          where: eq(property.userId, userId),
          columns: {
            id: true,
            name: true,
            address: true,
            propertyType: true,
            yearBuilt: true,
          },
        });

        // Get user's past projects
        const pastProjects = await db.query.job.findMany({
          where: eq(job.userId, userId),
          with: {
            tasks: {
              with: {
                trade: true,
              },
            },
            bids: {
              where: eq(bid.status, "ACCEPTED"),
              limit: 1,
            },
          },
          orderBy: [desc(job.createdAt)],
          limit: 10,
        });

        // Get popular trades/services
        const popularTrades = await db.query.trade.findMany({
          limit: 10,
        });

        // Generate recommendations based on property age and type
        const recommendations = generateProjectRecommendations(
          userProperties,
          pastProjects,
          popularTrades,
          projectType,
          budgetRange,
        );

        return JSON.stringify(
          {
            userProperties: userProperties.length,
            pastProjects: pastProjects.length,
            recommendations,
            insights: [
              "Based on your property characteristics and past projects",
              "Seasonal project timing considerations",
              "Budget optimization suggestions",
              "Contractor selection tips",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error getting project recommendations:", error);
        return "Failed to generate project recommendations. Please try again.";
      }
    },
  });

export const analyzeProjectBids = () =>
  tool({
    description:
      "Analyze bids for a specific project and provide recommendations",
    parameters: z.object({
      jobId: z.string().describe("The job ID to analyze bids for"),
      userId: z.string().describe("The user ID (must be the job owner)"),
    }),
    execute: async ({ jobId, userId }) => {
      try {
        // Verify user owns the job
        const jobDetails = await db.query.job.findFirst({
          where: and(eq(job.id, jobId), eq(job.userId, userId)),
          with: {
            bids: {
              with: {
                organization: {
                  with: {
                    trade: true,
                  },
                },
              },
            },
            tasks: {
              with: {
                trade: true,
              },
            },
          },
        });

        if (!jobDetails) {
          return "Job not found or you don't have permission to view it.";
        }

        if (jobDetails.bids.length === 0) {
          return "No bids have been submitted for this project yet.";
        }

        // Analyze bids
        const bidAnalysis = analyzeBids(jobDetails.bids, jobDetails.budget);

        // Generate recommendations
        const recommendations = generateBidRecommendations(
          jobDetails.bids,
          jobDetails,
        );

        return JSON.stringify(
          {
            jobTitle: jobDetails.name,
            totalBids: jobDetails.bids.length,
            budgetComparison: bidAnalysis,
            recommendations,
            insights: [
              "Bid price analysis and market comparison",
              "Contractor experience and reputation factors",
              "Timeline and availability considerations",
              "Value proposition evaluation",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error analyzing project bids:", error);
        return "Failed to analyze project bids. Please try again.";
      }
    },
  });

export const getProjectTimeline = () =>
  tool({
    description: "Generate a realistic project timeline and milestones",
    parameters: z.object({
      jobId: z.string().describe("The job ID to create timeline for"),
      userId: z.string().describe("The user ID (must be the job owner)"),
    }),
    execute: async ({ jobId, userId }) => {
      try {
        const jobDetails = await db.query.job.findFirst({
          where: and(eq(job.id, jobId), eq(job.userId, userId)),
          with: {
            tasks: {
              with: {
                trade: true,
              },
            },
            bids: {
              where: eq(bid.status, "ACCEPTED"),
              limit: 1,
            },
          },
        });

        if (!jobDetails) {
          return "Job not found or you don't have permission to view it.";
        }

        const timeline = generateProjectTimeline(jobDetails);

        return JSON.stringify(
          {
            projectName: jobDetails.name,
            estimatedDuration: timeline.totalDuration,
            phases: timeline.phases,
            milestones: timeline.milestones,
            considerations: [
              "Weather and seasonal factors",
              "Permit and inspection requirements",
              "Material delivery schedules",
              "Contractor availability",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error generating project timeline:", error);
        return "Failed to generate project timeline. Please try again.";
      }
    },
  });

// Helper functions
function generateProjectRecommendations(
  properties: any[],
  pastProjects: any[],
  trades: any[],
  projectType?: string,
  budgetRange?: any,
) {
  const recommendations = [];

  // Property age-based recommendations
  properties.forEach((property) => {
    const age = property.yearBuilt
      ? new Date().getFullYear() - property.yearBuilt
      : 0;

    if (age > 20) {
      recommendations.push({
        type: "maintenance",
        title: "HVAC System Inspection",
        reason: `Property built in ${property.yearBuilt} may need HVAC maintenance`,
        priority: "high",
        estimatedCost: "$200-500",
      });
    }

    if (age > 15) {
      recommendations.push({
        type: "improvement",
        title: "Energy Efficiency Upgrade",
        reason: "Older properties benefit from insulation and window upgrades",
        priority: "medium",
        estimatedCost: "$2000-8000",
      });
    }
  });

  // Seasonal recommendations
  const currentMonth = new Date().getMonth();
  if (currentMonth >= 2 && currentMonth <= 5) {
    // Spring
    recommendations.push({
      type: "seasonal",
      title: "Exterior Painting",
      reason: "Spring is ideal for exterior painting projects",
      priority: "medium",
      estimatedCost: "$3000-12000",
    });
  }

  return recommendations;
}

function analyzeBids(bids: any[], budget: number) {
  const amounts = bids.map((bid) => bid.amount).filter((amount) => amount > 0);

  if (amounts.length === 0) return null;

  const avgBid =
    amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
  const minBid = Math.min(...amounts);
  const maxBid = Math.max(...amounts);

  return {
    averageBid: avgBid,
    lowestBid: minBid,
    highestBid: maxBid,
    budgetComparison: {
      overBudget: amounts.filter((amount) => amount > budget).length,
      underBudget: amounts.filter((amount) => amount <= budget).length,
    },
    priceRange: maxBid - minBid,
  };
}

function generateBidRecommendations(bids: any[], job: any) {
  const recommendations = [];

  if (bids.length < 3) {
    recommendations.push(
      "Consider waiting for more bids to get better price comparison",
    );
  }

  const amounts = bids.map((bid) => bid.amount).filter((amount) => amount > 0);
  if (amounts.length > 0) {
    const avgAmount =
      amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
    const suspiciouslyLow = amounts.filter(
      (amount) => amount < avgAmount * 0.6,
    );

    if (suspiciouslyLow.length > 0) {
      recommendations.push(
        "Be cautious of bids significantly below average - verify scope and quality",
      );
    }
  }

  recommendations.push("Check contractor references and past work examples");
  recommendations.push(
    "Verify licensing and insurance before making a decision",
  );

  return recommendations;
}

function generateProjectTimeline(job: any) {
  const phases = [];
  let totalDuration = 0;

  // Planning phase
  phases.push({
    name: "Planning & Permits",
    duration: "1-2 weeks",
    tasks: ["Finalize design", "Obtain permits", "Order materials"],
  });
  totalDuration += 10; // days

  // Execution phases based on tasks
  job.tasks.forEach((task: any) => {
    const estimatedDays = getTaskDuration(task.trade.name);
    phases.push({
      name: task.name,
      duration: `${estimatedDays} days`,
      trade: task.trade.name,
    });
    totalDuration += estimatedDays;
  });

  // Final phase
  phases.push({
    name: "Final Inspection & Cleanup",
    duration: "2-3 days",
    tasks: ["Final inspection", "Touch-ups", "Cleanup"],
  });
  totalDuration += 3;

  return {
    totalDuration: `${Math.ceil(totalDuration / 7)} weeks`,
    phases,
    milestones: [
      "Project kickoff",
      "Permits approved",
      "Materials delivered",
      "50% completion",
      "Final inspection",
      "Project completion",
    ],
  };
}

function getTaskDuration(tradeName: string): number {
  const durations: { [key: string]: number } = {
    Plumbing: 3,
    Electrical: 2,
    Carpentry: 5,
    Painting: 3,
    Flooring: 4,
    Roofing: 7,
    HVAC: 4,
  };

  return durations[tradeName] || 3;
}
