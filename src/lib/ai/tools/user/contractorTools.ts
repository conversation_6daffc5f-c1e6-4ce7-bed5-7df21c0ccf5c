import { tool } from "ai";
import { and, desc, eq, gte, inArray } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { bid, job, membership } from "@/db/schema";

export const findRelevantJobs = () =>
  tool({
    description: "Find jobs relevant to contractor's skills and location",
    parameters: z.object({
      userId: z.string().describe("The contractor's user ID"),
      tradeIds: z
        .array(z.string())
        .optional()
        .describe("Specific trade IDs to filter by"),
      maxDistance: z
        .number()
        .optional()
        .default(50)
        .describe("Maximum distance in miles"),
      budgetRange: z
        .object({
          min: z.number().optional(),
          max: z.number().optional(),
        })
        .optional()
        .describe("Budget range filter"),
    }),
    execute: async ({ userId, tradeIds, maxDistance, budgetRange }) => {
      try {
        // Get contractor's organizations
        const userOrganizations = await db.query.membership.findMany({
          where: eq(membership.userId, userId),
          with: {
            organization: {
              with: {
                trade: true,
              },
            },
          },
        });

        if (userOrganizations.length === 0) {
          return "No organizations found for this user. Please create or join an organization first.";
        }

        // Get relevant trade IDs
        const relevantTradeIds =
          tradeIds || userOrganizations.map((m) => m.organization.tradeId);

        // Find jobs with tasks matching contractor's trades
        const jobQuery = db.query.job.findMany({
          where: eq(job.status, "PUBLISHED"),
          with: {
            tasks: {
              with: {
                trade: true,
              },
            },
            property: {
              columns: {
                address: true,
                location: true,
              },
            },
            bids: {
              columns: {
                id: true,
                organizationId: true,
              },
            },
          },
          orderBy: [desc(job.createdAt)],
          limit: 20,
        });

        const allJobs = await jobQuery;

        // Filter jobs by trade relevance and other criteria
        const relevantJobs = allJobs.filter((job) => {
          // Check if job has tasks matching contractor's trades
          const hasRelevantTasks = job.tasks.some((task) =>
            relevantTradeIds.includes(task.tradeId),
          );

          if (!hasRelevantTasks) return false;

          // Check budget range
          if (budgetRange) {
            if (budgetRange.min && job.budget < budgetRange.min) return false;
            if (budgetRange.max && job.budget > budgetRange.max) return false;
          }

          // Check if contractor hasn't already bid
          const contractorOrganizationIds = userOrganizations.map(
            (m) => m.organizationId,
          );
          const alreadyBid = job.bids.some((bid) =>
            contractorOrganizationIds.includes(bid.organizationId),
          );

          return !alreadyBid;
        });

        // Calculate match scores
        const jobsWithScores = relevantJobs.map((job) => ({
          ...job,
          matchScore: calculateJobMatchScore(job, userOrganizations),
          competitionLevel: job.bids.length,
        }));

        // Sort by match score
        jobsWithScores.sort((a, b) => b.matchScore - a.matchScore);

        return JSON.stringify(
          {
            totalRelevantJobs: jobsWithScores.length,
            jobs: jobsWithScores.slice(0, 10).map((job) => ({
              id: job.id,
              name: job.name,
              budget: job.budget,
              deadline: job.deadline,
              matchScore: job.matchScore,
              competitionLevel: job.competitionLevel,
              relevantTasks: job.tasks
                .filter((task) => relevantTradeIds.includes(task.tradeId))
                .map((task) => task.trade.name),
              location: job.property?.address,
            })),
            insights: [
              "Jobs are ranked by relevance to your skills",
              "Competition level indicates number of existing bids",
              "Consider bidding on high-match, low-competition jobs first",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error finding relevant jobs:", error);
        return "Failed to find relevant jobs. Please try again.";
      }
    },
  });

export const analyzeBidCompetition = () =>
  tool({
    description:
      "Analyze competition for a specific job to help with bidding strategy",
    parameters: z.object({
      jobId: z.string().describe("The job ID to analyze"),
      userId: z.string().describe("The contractor's user ID"),
    }),
    execute: async ({ jobId, userId }) => {
      try {
        const jobDetails = await db.query.job.findFirst({
          where: eq(job.id, jobId),
          with: {
            tasks: {
              with: {
                trade: true,
              },
            },
            bids: {
              with: {
                organization: {
                  with: {
                    trade: true,
                  },
                },
              },
            },
          },
        });

        if (!jobDetails) {
          return "Job not found.";
        }

        if (jobDetails.status !== "PUBLISHED") {
          return "This job is no longer accepting bids.";
        }

        // Analyze existing bids
        const bidAnalysis = {
          totalBids: jobDetails.bids.length,
          averageBid: 0,
          bidRange: { min: 0, max: 0 },
          competitorTrades: [] as string[],
        };

        if (jobDetails.bids.length > 0) {
          const amounts = jobDetails.bids
            .map((bid) => bid.amount)
            .filter((amount) => amount > 0);

          if (amounts.length > 0) {
            bidAnalysis.averageBid =
              amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
            bidAnalysis.bidRange.min = Math.min(...amounts);
            bidAnalysis.bidRange.max = Math.max(...amounts);
          }

          bidAnalysis.competitorTrades = [
            ...new Set(
              jobDetails.bids.map((bid) => bid.organization.trade.name),
            ),
          ];
        }

        // Generate bidding recommendations
        const recommendations = generateBiddingRecommendations(
          jobDetails,
          bidAnalysis,
        );

        return JSON.stringify(
          {
            jobTitle: jobDetails.name,
            jobBudget: jobDetails.budget,
            deadline: jobDetails.deadline,
            competition: bidAnalysis,
            requiredTrades: jobDetails.tasks.map((task) => task.trade.name),
            recommendations,
            insights: [
              "Competitive positioning analysis",
              "Optimal bid range suggestions",
              "Differentiation opportunities",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error analyzing bid competition:", error);
        return "Failed to analyze bid competition. Please try again.";
      }
    },
  });

export const getContractorPerformance = () =>
  tool({
    description:
      "Get performance metrics and insights for contractor's business",
    parameters: z.object({
      userId: z.string().describe("The contractor's user ID"),
      timeframe: z
        .enum(["30d", "90d", "1y"])
        .optional()
        .default("90d")
        .describe("Time frame for analysis"),
    }),
    execute: async ({ userId, timeframe }) => {
      try {
        const startDate = new Date();
        switch (timeframe) {
          case "30d":
            startDate.setDate(startDate.getDate() - 30);
            break;
          case "90d":
            startDate.setDate(startDate.getDate() - 90);
            break;
          case "1y":
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;
        }

        // Get contractor's organizations
        const userOrganizations = await db.query.membership.findMany({
          where: eq(membership.userId, userId),
          with: {
            organization: true,
          },
        });

        if (userOrganizations.length === 0) {
          return "No organizations found for this user.";
        }

        const organizationIds = userOrganizations.map((m) => m.organizationId);

        // Get bid performance
        const bids = await db.query.bid.findMany({
          where: and(
            inArray(bid.organizationId, organizationIds),
            gte(bid.createdAt, startDate),
          ),
          with: {
            job: {
              columns: {
                name: true,
                budget: true,
                status: true,
              },
            },
          },
        });

        // Calculate metrics
        const metrics = {
          totalBids: bids.length,
          acceptedBids: bids.filter((bid) => bid.status === "ACCEPTED").length,
          rejectedBids: bids.filter((bid) => bid.status === "REJECTED").length,
          pendingBids: bids.filter((bid) => bid.status === "PROPOSED").length,
          totalBidValue: bids.reduce((sum, bid) => sum + (bid.amount || 0), 0),
          wonBidValue: bids
            .filter((bid) => bid.status === "ACCEPTED")
            .reduce((sum, bid) => sum + (bid.amount || 0), 0),
        };

        const winRate =
          metrics.totalBids > 0
            ? (metrics.acceptedBids / metrics.totalBids) * 100
            : 0;

        // Generate insights
        const insights = generatePerformanceInsights(metrics, winRate);

        return JSON.stringify(
          {
            timeframe,
            organizations: userOrganizations.map((m) => m.organization.name),
            performance: {
              ...metrics,
              winRate: `${winRate.toFixed(1)}%`,
            },
            insights,
            recommendations: [
              winRate < 20
                ? "Consider reviewing bid strategy - win rate is below average"
                : "Good win rate performance",
              metrics.pendingBids > 5
                ? "Follow up on pending bids"
                : "Bid pipeline looks healthy",
              "Focus on building long-term client relationships",
            ],
          },
          null,
          2,
        );
      } catch (error) {
        console.error("Error getting contractor performance:", error);
        return "Failed to get contractor performance metrics. Please try again.";
      }
    },
  });

// Helper functions
function calculateJobMatchScore(job: any, userOrganizations: any[]): number {
  let score = 0;

  // Base score for having relevant tasks
  const userTradeIds = userOrganizations.map((m) => m.organization.tradeId);
  const matchingTasks = job.tasks.filter((task: any) =>
    userTradeIds.includes(task.tradeId),
  );

  score += (matchingTasks.length / job.tasks.length) * 50;

  // Bonus for fewer competing bids
  if (job.bids.length === 0) score += 30;
  else if (job.bids.length < 3) score += 20;
  else if (job.bids.length < 5) score += 10;

  // Bonus for recent posting
  const daysSincePosted =
    (Date.now() - new Date(job.createdAt).getTime()) / (1000 * 60 * 60 * 24);
  if (daysSincePosted < 1) score += 20;
  else if (daysSincePosted < 3) score += 10;

  return Math.min(score, 100);
}

function generateBiddingRecommendations(job: any, bidAnalysis: any): string[] {
  const recommendations = [];

  if (bidAnalysis.totalBids === 0) {
    recommendations.push(
      "First to bid - great opportunity to make a strong impression",
    );
  } else if (bidAnalysis.totalBids < 3) {
    recommendations.push(
      "Low competition - good chance of winning with competitive bid",
    );
  } else {
    recommendations.push(
      "High competition - focus on value proposition and differentiation",
    );
  }

  if (bidAnalysis.averageBid > 0) {
    const suggestedRange = {
      min: Math.round(bidAnalysis.averageBid * 0.9),
      max: Math.round(bidAnalysis.averageBid * 1.1),
    };
    recommendations.push(
      `Consider bidding in range $${suggestedRange.min.toLocaleString()} - $${suggestedRange.max.toLocaleString()}`,
    );
  }

  recommendations.push(
    "Highlight your unique qualifications and past similar work",
  );
  recommendations.push("Provide detailed timeline and project approach");

  return recommendations;
}

function generatePerformanceInsights(metrics: any, winRate: number): string[] {
  const insights = [];

  if (winRate > 30) {
    insights.push("Excellent win rate - your bidding strategy is working well");
  } else if (winRate > 15) {
    insights.push(
      "Good win rate - consider minor adjustments to improve further",
    );
  } else {
    insights.push(
      "Win rate could be improved - review pricing and proposal quality",
    );
  }

  if (metrics.totalBids < 5) {
    insights.push(
      "Consider bidding on more projects to increase opportunities",
    );
  }

  if (metrics.pendingBids > metrics.acceptedBids) {
    insights.push(
      "Many pending bids - follow up with clients for faster decisions",
    );
  }

  return insights;
}
